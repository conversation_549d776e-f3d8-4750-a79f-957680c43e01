'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  ChartBarIcon,
  CheckIcon,
  ClockIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface Agent {
  id: string;
  name: string;
  status: string;
  progress: number;
  lastUpdate: string;
}

interface Report {
  id: string;
  stage: string;
  title: string;
  content: string;
  timestamp: string;
  confidence: number;
  recommendations: string[];
}

interface TradingDecision {
  action: string;
  confidence: number;
  reasoning: string;
  targetPrice: number | null;
  stopLoss: number | null;
  riskLevel: string;
  timestamp: string;
}

interface AnalysisState {
  analysisId: string;
  config: any;
  status: string;
  currentStage: string;
  progress: number;
  isComplete: boolean;
  startTime: string;
  endTime?: string;
  agents: Agent[];
  reports: Report[];
  tradingDecision: TradingDecision | null;
  error: string | null;
}

export default function AnalysisPage() {
  const router = useRouter();
  const params = useParams();
  const analysisId = params.id as string;

  const [analysisState, setAnalysisState] = useState<AnalysisState | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 轮询获取分析状态
  useEffect(() => {
    if (!analysisId) return;

    const fetchAnalysisState = async () => {
      try {
        const response = await fetch(`/api/langgraph/analysis/status?id=${analysisId}`);
        if (response.ok) {
          const data = await response.json();
          setAnalysisState(data);
          setLoading(false);
        } else {
          throw new Error('获取分析状态失败1');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '未知错误');
        setLoading(false);
      }
    };

    // 立即获取一次
    fetchAnalysisState();

    // 如果分析未完成，每2秒轮询一次
    const interval = setInterval(() => {
      if (analysisState?.isComplete) {
        clearInterval(interval);
        return;
      }
      fetchAnalysisState();
    }, 2000);

    return () => clearInterval(interval);
  }, [analysisId, analysisState?.isComplete]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'warning';
      case 'failed': return 'danger';
      default: return 'default';
    }
  };

  const getAgentStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />;
      case 'completed': return <CheckIcon className="h-4 w-4 text-green-500" />;
      case 'waiting': return <ClockIcon className="h-4 w-4 text-slate-400" />;
      default: return <div className="w-3 h-3 bg-slate-300 rounded-full" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'buy': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'sell': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'hold': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      default: return 'text-slate-600 bg-slate-100 dark:bg-slate-900/20';
    }
  };

  const getActionText = (action: string) => {
    switch (action) {
      case 'buy': return '买入';
      case 'sell': return '卖出';
      case 'hold': return '持有';
      default: return action;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-slate-600 dark:text-slate-400">加载分析状态...</p>
        </div>
      </div>
    );
  }

  if (error || !analysisState) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
            加载失败
          </h2>
          <p className="text-slate-600 dark:text-slate-400 mb-4">
            {error || '未找到分析数据'}
          </p>
          <Button onClick={() => router.push('/')}>
            返回首页
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4 py-8">

        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                分析报告 - {analysisState.config.ticker}
              </h1>
              <p className="text-slate-600 dark:text-slate-400">
                分析ID: {analysisState.analysisId}
              </p>
            </div>
            <Badge variant={getStatusColor(analysisState.status)}>
              {analysisState.status === 'completed' ? '已完成' :
                analysisState.status === 'running' ? '进行中' :
                  analysisState.status === 'failed' ? '失败' : '等待中'}
            </Badge>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 整体进度 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <ChartBarIcon className="h-5 w-5" />
                    <span>分析进度</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">整体进度</span>
                      <span className="text-sm text-slate-600 dark:text-slate-400">
                        {analysisState.progress}%
                      </span>
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3">
                      <motion.div
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${analysisState.progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      当前阶段: {analysisState.currentStage}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* 智能体状态 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <span>👥</span>
                    <span>智能体团队状态</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {analysisState.agents.map((agent, index) => (
                      <motion.div
                        key={agent.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 * index }}
                        className="p-4 border border-slate-200 dark:border-slate-600 rounded-lg"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            {getAgentStatusIcon(agent.status)}
                            <span className="font-medium text-slate-900 dark:text-white">
                              {agent.name}
                            </span>
                          </div>
                          <span className="text-sm text-slate-500">
                            {agent.progress}%
                          </span>
                        </div>
                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <motion.div
                            className="bg-blue-500 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${agent.progress}%` }}
                            transition={{ duration: 0.3 }}
                          />
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* 分析报告 */}
            {analysisState.reports.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <DocumentTextIcon className="h-5 w-5" />
                      <span>分析报告</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analysisState.reports.map((report, index) => (
                        <motion.div
                          key={report.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * index }}
                          className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold text-slate-900 dark:text-white">
                              {report.title}
                            </h4>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-slate-500">
                                置信度: {Math.round(report.confidence * 100)}%
                              </span>
                              <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                                <div
                                  className="bg-green-500 h-2 rounded-full"
                                  style={{ width: `${report.confidence * 100}%` }}
                                />
                              </div>
                            </div>
                          </div>
                          <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
                            {report.content}
                          </p>
                          {report.recommendations.length > 0 && (
                            <div className="space-y-1">
                              <h5 className="text-xs font-medium text-slate-700 dark:text-slate-300">
                                建议:
                              </h5>
                              {report.recommendations.map((rec, recIndex) => (
                                <p key={recIndex} className="text-xs text-slate-600 dark:text-slate-400 pl-2">
                                  • {rec}
                                </p>
                              ))}
                            </div>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* 交易决策 */}
            {analysisState.tradingDecision && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <CurrencyDollarIcon className="h-5 w-5" />
                      <span>交易决策</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* 主要决策 */}
                      <div className="text-center">
                        <div className={`inline-flex items-center px-6 py-3 rounded-full text-lg font-bold ${getActionColor(analysisState.tradingDecision.action)}`}>
                          {getActionText(analysisState.tradingDecision.action)}
                        </div>
                        <div className="mt-2 flex items-center justify-center space-x-2">
                          <span className="text-sm text-slate-600 dark:text-slate-400">置信度:</span>
                          <div className="flex items-center space-x-1">
                            <div className="w-24 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full"
                                style={{ width: `${analysisState.tradingDecision.confidence * 100}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium">
                              {Math.round(analysisState.tradingDecision.confidence * 100)}%
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* 详细信息 */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {analysisState.tradingDecision.targetPrice && (
                          <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                            <div className="text-sm text-slate-600 dark:text-slate-400">目标价格</div>
                            <div className="text-lg font-semibold text-slate-900 dark:text-white">
                              ${analysisState.tradingDecision.targetPrice.toFixed(2)}
                            </div>
                          </div>
                        )}

                        {analysisState.tradingDecision.stopLoss && (
                          <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                            <div className="text-sm text-slate-600 dark:text-slate-400">止损价格</div>
                            <div className="text-lg font-semibold text-slate-900 dark:text-white">
                              ${analysisState.tradingDecision.stopLoss.toFixed(2)}
                            </div>
                          </div>
                        )}

                        <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                          <div className="text-sm text-slate-600 dark:text-slate-400">风险等级</div>
                          <div className={`text-lg font-semibold ${analysisState.tradingDecision.riskLevel === 'low' ? 'text-green-600' :
                              analysisState.tradingDecision.riskLevel === 'medium' ? 'text-yellow-600' :
                                'text-red-600'
                            }`}>
                            {analysisState.tradingDecision.riskLevel === 'low' ? '低风险' :
                              analysisState.tradingDecision.riskLevel === 'medium' ? '中风险' : '高风险'}
                          </div>
                        </div>
                      </div>

                      {/* 决策理由 */}
                      <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <h4 className="font-semibold text-slate-900 dark:text-white mb-2">
                          决策理由
                        </h4>
                        <p className="text-sm text-slate-700 dark:text-slate-300">
                          {analysisState.tradingDecision.reasoning}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 分析配置 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">分析配置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">股票代码</span>
                    <code className="bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                      {analysisState.config.ticker}
                    </code>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">分析周期</span>
                    <span>
                      {analysisState.config.analysisPeriod === 'custom'
                        ? `${analysisState.config.customStartDate} 至 ${analysisState.config.customEndDate}`
                        : analysisState.config.analysisPeriod || analysisState.config.analysisDate
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">研究深度</span>
                    <span className="capitalize">{analysisState.config.researchDepth}</span>
                  </div>
                  <div>
                    <div className="text-slate-600 dark:text-slate-400 mb-1">选择的分析师</div>
                    <div className="space-y-1">
                      {analysisState.config.selectedAnalysts?.map((analyst: string) => (
                        <div key={analyst} className="text-xs bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                          {analyst}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* 时间信息 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">时间信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">开始时间</span>
                    <span>{new Date(analysisState.startTime).toLocaleString()}</span>
                  </div>
                  {analysisState.endTime && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">结束时间</span>
                      <span>{new Date(analysisState.endTime).toLocaleString()}</span>
                    </div>
                  )}
                  {analysisState.endTime && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">耗时</span>
                      <span>
                        {Math.round((new Date(analysisState.endTime).getTime() - new Date(analysisState.startTime).getTime()) / 1000)}秒
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
