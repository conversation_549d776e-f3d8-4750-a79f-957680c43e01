'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AgentStatusPanel } from './AgentStatusPanel';
import { AnalysisProgress } from './AnalysisProgress';
import { RealtimeDataPanel } from './RealtimeDataPanel';
import { ReportViewer } from './ReportViewer';
import { TradingDecision } from './TradingDecision';
import { useTradingAnalysis } from '@/hooks/useTradingAnalysis';
import { LangGraphChat } from '@/components/langgraph/LangGraphChat';
import {
  WorkflowVisualization,
  defaultTradingWorkflowNodes,
  defaultTradingWorkflowEdges,
} from '@/components/langgraph/WorkflowVisualization';

interface TradingDashboardProps {
  config: any;
  onBack: () => void;
}

export function TradingDashboard({ config, onBack }: TradingDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const hasStartedAnalysis = useRef(false);

  const { analysisState, agentStatuses, reports, finalDecision, startAnalysis, isAnalyzing } =
    useTradingAnalysis(config);

  useEffect(() => {
    // 自动开始分析 - 只在组件挂载时执行一次
    if (!hasStartedAnalysis.current && !isAnalyzing) {
      hasStartedAnalysis.current = true;
      startAnalysis();
    }
  }, [startAnalysis, isAnalyzing]);

  const tabs = [
    { id: 'overview', name: '总览', icon: '📊' },
    { id: 'agents', name: '代理状态', icon: '🤖' },
    { id: 'data', name: '实时数据', icon: '📈' },
    { id: 'reports', name: '分析报告', icon: '📋' },
    { id: 'decision', name: '交易决策', icon: '💼' },
    { id: 'langgraph', name: 'LangGraph', icon: '🧠' },
    { id: 'workflow', name: '工作流', icon: '🔄' },
  ];

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            {config.ticker} 交易分析
          </h1>
          <p className="text-slate-600 dark:text-slate-400">分析日期: {config.analysisDate}</p>
        </div>

        <div className="flex items-center space-x-4">
          {isAnalyzing && (
            <div className="flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm">分析进行中...</span>
            </div>
          )}
        </div>
      </motion.div>

      {/* Progress Bar */}
      <AnalysisProgress
        currentStage={analysisState.currentStage}
        progress={analysisState.progress}
        isComplete={analysisState.isComplete}
      />

      {/* Tab Navigation */}
      <div className="border-b border-slate-200 dark:border-slate-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 分析配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>⚙️</span>
                  <span>分析配置</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">股票标识:</span>
                    <code className="bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded font-semibold">
                      {config.ticker}
                    </code>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">分析日期:</span>
                    <span className="font-semibold">{config.analysisDate}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">选择的分析师:</span>
                    <span className="font-semibold">{config.selectedAnalysts.length}个</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">研究深度:</span>
                    <span className="font-semibold">
                      {config.researchDepth === 'quick'
                        ? '快速'
                        : config.researchDepth === 'standard'
                        ? '标准'
                        : '深度'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">在线工具:</span>
                    <span
                      className={`font-semibold ${
                        config.onlineTools ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {config.onlineTools ? '✅ 启用' : '❌ 禁用'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">LLM 提供商:</span>
                    <span className="font-semibold">{config.llmProvider || 'OpenAI'}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600 dark:text-slate-400">辩论轮数:</span>
                    <span className="font-semibold">{config.maxDebateRounds || 3}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 当前状态 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📊</span>
                  <span>当前状态</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="relative w-20 h-20 mx-auto mb-4">
                      <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          className="text-slate-200 dark:text-slate-700"
                          stroke="currentColor"
                          strokeWidth="3"
                          fill="none"
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        />
                        <path
                          className="text-blue-600"
                          stroke="currentColor"
                          strokeWidth="3"
                          fill="none"
                          strokeDasharray={`${analysisState.progress}, 100`}
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xl font-bold text-slate-900 dark:text-white">
                          {analysisState.progress}%
                        </span>
                      </div>
                    </div>
                    <div className="text-slate-600 dark:text-slate-400">分析进度</div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      当前阶段: {analysisState.currentStage}
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3">
                      <motion.div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500"
                        initial={{ width: 0 }}
                        animate={{ width: `${analysisState.progress}%` }}
                      />
                    </div>
                  </div>

                  {/* 状态指示器 */}
                  <div className="flex items-center justify-center space-x-2">
                    {isAnalyzing ? (
                      <>
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm text-green-600 dark:text-green-400">运行中</span>
                      </>
                    ) : analysisState.isComplete ? (
                      <>
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm text-blue-600 dark:text-blue-400">已完成</span>
                      </>
                    ) : (
                      <>
                        <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                        <span className="text-sm text-slate-600 dark:text-slate-400">等待中</span>
                      </>
                    )}
                  </div>

                  {analysisState.isComplete && finalDecision && (
                    <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <div className="text-sm font-medium text-green-800 dark:text-green-200">
                        ✅ 分析完成！
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-400 mt-1">
                        交易决策已生成，请查看"交易决策"标签页
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 统计信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📈</span>
                  <span>统计信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {agentStatuses.filter((agent) => agent.status === 'completed').length}
                      </div>
                      <div className="text-xs text-slate-600 dark:text-slate-400">已完成代理</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{reports.length}</div>
                      <div className="text-xs text-slate-600 dark:text-slate-400">生成报告</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {agentStatuses.filter((agent) => agent.status === 'running').length}
                      </div>
                      <div className="text-xs text-slate-600 dark:text-slate-400">运行中代理</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">
                        {finalDecision ? '1' : '0'}
                      </div>
                      <div className="text-xs text-slate-600 dark:text-slate-400">交易决策</div>
                    </div>
                  </div>

                  {/* 分析师状态概览 */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      分析师状态
                    </h4>
                    <div className="space-y-1">
                      {config.selectedAnalysts.map((analyst: string) => {
                        const status = agentStatuses.find((agent) => agent.id === analyst);
                        return (
                          <div key={analyst} className="flex items-center justify-between text-sm">
                            <span className="text-slate-600 dark:text-slate-400">
                              {analyst === 'market'
                                ? '市场分析师'
                                : analyst === 'social'
                                ? '社交媒体分析师'
                                : analyst === 'news'
                                ? '新闻分析师'
                                : analyst === 'fundamentals'
                                ? '基本面分析师'
                                : analyst}
                            </span>
                            <div className="flex items-center space-x-1">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  status?.status === 'completed'
                                    ? 'bg-green-500'
                                    : status?.status === 'running'
                                    ? 'bg-blue-500 animate-pulse'
                                    : status?.status === 'error'
                                    ? 'bg-red-500'
                                    : 'bg-slate-400'
                                }`}
                              ></div>
                              <span className="text-xs">{status?.progress || 0}%</span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'agents' && (
          <AgentStatusPanel
            agentStatuses={agentStatuses}
            selectedAnalysts={config.selectedAnalysts}
          />
        )}

        {activeTab === 'data' && (
          <RealtimeDataPanel ticker={config.ticker} analysisDate={config.analysisDate} />
        )}

        {activeTab === 'reports' && (
          <ReportViewer reports={reports} selectedAnalysts={config.selectedAnalysts} />
        )}

        {activeTab === 'decision' && (
          <TradingDecision
            decision={finalDecision}
            isComplete={analysisState.isComplete}
            ticker={config.ticker}
          />
        )}

        {activeTab === 'langgraph' && (
          <LangGraphChat
            ticker={config.ticker}
            onAnalysisComplete={(result) => {
              console.log('LangGraph分析完成:', result);
              // 可以将结果集成到主分析流程中
            }}
          />
        )}

        {activeTab === 'workflow' && (
          <WorkflowVisualization
            nodes={defaultTradingWorkflowNodes}
            edges={defaultTradingWorkflowEdges}
            currentNode={analysisState.currentStage}
          />
        )}
      </motion.div>
    </div>
  );
}
