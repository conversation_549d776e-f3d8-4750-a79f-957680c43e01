'use client';

import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { authApi, taskApi } from '@/lib/api';
import { Task } from '@/types/database';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

// 消息数据类型定义
interface Message {
  id: number;
  message_id: string;
  conversation_id: string;
  task_id: string;
  message_type: 'human' | 'ai' | 'system' | 'tool';
  content: string;
  metadata: any;
  sequence_number: number;
  parent_message_id: string | null;
  created_at: string;
}

export default function TasksPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [authLoading, setAuthLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMessages, setSelectedMessages] = useState<Message[]>([]);
  const [showMessagesModal, setShowMessagesModal] = useState(false);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [startingTask, setStartingTask] = useState<string | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showTaskModal, setShowTaskModal] = useState(false);

  // 检查用户登录状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const data = await authApi.getCurrentUser();
        setUser(data.user);
        setAuthLoading(false);
      } catch (error) {
        console.error('Auth check failed:', error);
        router.push('/login');
        setAuthLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  // 获取任务列表
  const fetchTasks = async () => {
    try {
      setLoading(true);
      const data = await taskApi.getTasks();

      // 登录用户：显示个人任务和系统任务
      const userTasks = data.filter((task: Task) =>
        task.created_by === user?.email ||
        task.created_by === user?.username ||
        task.created_by === 'system'
      );
      setTasks(userTasks);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
      toast.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看任务详情
  const viewTaskDetails = (task: Task) => {
    setSelectedTask(task);
    setShowTaskModal(true);
  };

  // 查看任务消息
  const viewMessages = async (taskId: string) => {
    try {
      setLoadingMessages(true);
      const response = await fetch(`/api/database/messages?task_id=${taskId}&include_metadata=true`);
      if (!response.ok) {
        throw new Error('获取消息失败');
      }
      const data = await response.json();
      setSelectedMessages(data.messages || []);
      setShowMessagesModal(true);
    } catch (err) {
      toast.error('获取消息失败');
      console.error('获取消息失败:', err);
    } finally {
      setLoadingMessages(false);
    }
  };

  // 开始任务
  const startTask = async (task: Task) => {
    try {
      setStartingTask(task.task_id);

      // 首先更新任务状态为运行中
      await taskApi.updateTaskStatus(task.task_id, 'running');

      // 调用分析接口，直接传递task_id
      try {
        await taskApi.startAnalysis({
          ticker: task.ticker,
          task_id: task.task_id,
          config: {
            analysisType: 'comprehensive',
            includeRisk: true,
            includeSentiment: true,
            researchDepth: task.research_depth,
            analysisPeriod: task.analysis_period,
          },
        });
      } catch (analysisError) {
        // 如果启动失败，恢复任务状态
        await taskApi.updateTaskStatus(task.task_id, 'pending');
        throw analysisError;
      }
      toast.success('任务启动成功！分析已开始处理。');

      // 刷新任务列表
      fetchTasks();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '启动任务失败';
      toast.error(`启动任务失败: ${errorMessage}`);
      console.error('启动任务失败:', err);
    } finally {
      setStartingTask(null);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [user, fetchTasks]);

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载任务列表中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">❌ 加载失败</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchTasks}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">任务管理</h1>
          <p className="mt-2 text-gray-600">欢迎 {user?.username}，管理和操作您的分析任务</p>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-gray-900">{tasks.length}</div>
            <div className="text-sm text-gray-600">总任务数</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-green-600">
              {tasks.filter(t => t.status === 'completed').length}
            </div>
            <div className="text-sm text-gray-600">已完成</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-blue-600">
              {tasks.filter(t => t.status === 'running').length}
            </div>
            <div className="text-sm text-gray-600">运行中</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-yellow-600">
              {tasks.filter(t => t.status === 'pending').length}
            </div>
            <div className="text-sm text-gray-600">待处理</div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between items-center mb-6">
          <button
            onClick={fetchTasks}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            刷新列表
          </button>
          <button
            onClick={() => router.push('/create-task')}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
          >
            创建新任务
          </button>
        </div>

        {/* 任务表格 */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    任务ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    股票代码
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    标题
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    研究深度
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    分析周期
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建者
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {tasks.map((task) => (
                  <tr key={task.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                      {task.task_id.substring(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {task.ticker}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {task.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${task.status === 'completed' ? 'bg-green-100 text-green-800' :
                            task.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              task.status === 'running' ? 'bg-blue-100 text-blue-800' :
                                'bg-red-100 text-red-800'
                          }`}
                      >
                        {task.status === 'completed' ? '已完成' :
                          task.status === 'pending' ? '待处理' :
                            task.status === 'running' ? '运行中' : '失败'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.research_depth || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.analysis_period || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.created_at ? format(new Date(task.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN }) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.created_by || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => viewTaskDetails(task)}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="查看详情"
                        >
                          详情
                        </button>
                        <a
                          href={`/messages?task_id=${task.task_id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="查看消息"
                        >
                          消息
                        </a>
                        <button
                          onClick={() => startTask(task)}
                          disabled={startingTask === task.task_id}
                          className="text-green-600 hover:text-green-900 disabled:opacity-50"
                          title="开始分析"
                        >
                          {startingTask === task.task_id ? '启动中...' : '开始'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {tasks.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">暂无任务数据</p>
              <button
                onClick={() => router.push('/create-task')}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                创建第一个任务
              </button>
            </div>
          )}
        </div>

        {/* 任务详情模态框 */}
        {showTaskModal && selectedTask && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">任务详情</h3>
                  <button
                    onClick={() => setShowTaskModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">关闭</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">任务ID</label>
                      <p className="mt-1 text-sm text-gray-900 font-mono">{selectedTask.task_id}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">股票代码</label>
                      <p className="mt-1 text-sm text-gray-900 font-semibold">{selectedTask.ticker}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">标题</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedTask.title}</p>
                  </div>

                  {selectedTask.description && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">描述</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedTask.description}</p>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">状态</label>
                      <span
                        className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${selectedTask.status === 'completed' ? 'bg-green-100 text-green-800' :
                            selectedTask.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              selectedTask.status === 'running' ? 'bg-blue-100 text-blue-800' :
                                'bg-red-100 text-red-800'
                          }`}
                      >
                        {selectedTask.status === 'completed' ? '已完成' :
                          selectedTask.status === 'pending' ? '待处理' :
                            selectedTask.status === 'running' ? '运行中' : '失败'}
                      </span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">优先级</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedTask.priority || '-'}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">研究深度</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedTask.research_depth || '-'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">分析周期</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedTask.analysis_period || '-'}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">创建时间</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {selectedTask.created_at ? format(new Date(selectedTask.created_at), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN }) : '-'}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">创建者</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedTask.created_by || '-'}</p>
                    </div>
                  </div>

                  {selectedTask.started_at && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">开始时间</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {format(new Date(selectedTask.started_at), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
                      </p>
                    </div>
                  )}

                  {selectedTask.completed_at && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">完成时间</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {format(new Date(selectedTask.completed_at), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
                      </p>
                    </div>
                  )}

                  {selectedTask.error_message && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">错误信息</label>
                      <p className="mt-1 text-sm text-red-600 bg-red-50 p-2 rounded">{selectedTask.error_message}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 消息模态框 */}
        {showMessagesModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">任务消息</h3>
                  <button
                    onClick={() => setShowMessagesModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">关闭</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {selectedMessages.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">该任务暂无消息</p>
                  ) : (
                    <div className="space-y-4">
                      {selectedMessages.map((message) => (
                        <div
                          key={message.id}
                          className={`p-4 rounded-lg ${message.message_type === 'human'
                              ? 'bg-blue-50 border-l-4 border-blue-400'
                              : message.message_type === 'ai'
                                ? 'bg-green-50 border-l-4 border-green-400'
                                : 'bg-gray-50 border-l-4 border-gray-400'
                            }`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <span className={`text-xs font-medium px-2 py-1 rounded ${message.message_type === 'human' ? 'bg-blue-100 text-blue-800' :
                                message.message_type === 'ai' ? 'bg-green-100 text-green-800' :
                                  'bg-gray-100 text-gray-800'
                              }`}>
                              {message.message_type === 'human' ? '用户' :
                                message.message_type === 'ai' ? 'AI' :
                                  message.message_type === 'system' ? '系统' : '工具'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {format(new Date(message.created_at), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
                            </span>
                          </div>
                          <div className="text-sm text-gray-900 whitespace-pre-wrap">
                            {message.content}
                          </div>
                          {message.metadata && (
                            <details className="mt-2">
                              <summary className="text-xs text-gray-500 cursor-pointer">元数据</summary>
                              <pre className="text-xs text-gray-600 mt-1 bg-gray-100 p-2 rounded overflow-x-auto">
                                {JSON.stringify(message.metadata, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
