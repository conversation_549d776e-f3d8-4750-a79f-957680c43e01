// Simple test script for data APIs
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/data';
const TEST_TICKER = '600519';

async function testDataAPIs() {
  console.log('🧪 测试数据 API...\n');

  // Test 1: API Documentation
  try {
    console.log('📖 测试 API 文档...');
    const response = await axios.get(`${BASE_URL}`);
    console.log('✅ API 文档获取成功');
    console.log(`   - 端点数量: ${Object.keys(response.data.endpoints).length}`);
    console.log(`   - 数据类型: ${Object.keys(response.data.dataTypes).length}`);
  } catch (error) {
    console.log('❌ API 文档获取失败:', error.message);
  }

  // Test 2: Stock Data API
  try {
    console.log('\n📈 测试股票数据 API...');
    const response = await axios.get(`${BASE_URL}/stock/${TEST_TICKER}?period=1m`);
    console.log('✅ 股票数据获取成功');
    console.log(`   - 股票: ${response.data.name} (${response.data.ticker})`);
    console.log(`   - 数据点: ${response.data.count}`);
    console.log(`   - 周期: ${response.data.period}`);
    if (response.data.stats) {
      console.log(`   - 当前价格: ${response.data.stats.currentPrice}`);
      console.log(`   - 总回报: ${response.data.stats.totalReturn}`);
    }
  } catch (error) {
    console.log('❌ 股票数据获取失败:', error.response?.data?.message || error.message);
  }

  // Test 3: News Data API
  try {
    console.log('\n📰 测试新闻数据 API...');
    const response = await axios.get(`${BASE_URL}/news/${TEST_TICKER}?limit=5&days=7`);
    console.log('✅ 新闻数据获取成功');
    console.log(`   - 股票: ${response.data.name} (${response.data.ticker})`);
    console.log(`   - 新闻数量: ${response.data.count}`);
    console.log(`   - 时间范围: ${response.data.period}`);
    if (response.data.stats) {
      console.log(`   - 平均情绪: ${response.data.stats.avgSentiment}`);
      console.log(`   - 情绪标签: ${response.data.stats.sentimentLabel}`);
    }
  } catch (error) {
    console.log('❌ 新闻数据获取失败:', error.response?.data?.message || error.message);
  }

  // Test 4: Technical Indicators API
  try {
    console.log('\n📊 测试技术指标 API...');
    const response = await axios.get(
      `${BASE_URL}/technical/${TEST_TICKER}?indicators=ma,rsi&days=60`
    );
    console.log('✅ 技术指标获取成功');
    console.log(`   - 股票: ${response.data.name} (${response.data.ticker})`);
    console.log(`   - 数据点: ${response.data.dataPoints}`);
    console.log(`   - 指标: ${Object.keys(response.data.indicators).join(', ')}`);
    if (response.data.analysis) {
      console.log(`   - 趋势: ${response.data.analysis.trend}`);
      console.log(`   - 当前价格: ${response.data.analysis.currentPrice}`);
    }
  } catch (error) {
    console.log('❌ 技术指标获取失败:', error.response?.data?.message || error.message);
  }

  // Test 5: Fundamental Data API
  try {
    console.log('\n💰 测试基本面数据 API...');
    const response = await axios.get(`${BASE_URL}/fundamentals/${TEST_TICKER}?type=valuation`);
    console.log('✅ 基本面数据获取成功');
    console.log(`   - 股票: ${response.data.name} (${response.data.ticker})`);
    console.log(`   - 数据类型: ${response.data.period}`);
    if (response.data.fundamentals && response.data.fundamentals.valuation) {
      const valuation = response.data.fundamentals.valuation;
      console.log(`   - PE: ${valuation.pe || 'N/A'}`);
      console.log(`   - PB: ${valuation.pb || 'N/A'}`);
    }
    if (response.data.analysis) {
      console.log(`   - 综合评级: ${response.data.analysis.overallRating}`);
      console.log(`   - 建议: ${response.data.analysis.recommendation}`);
    }
  } catch (error) {
    console.log('❌ 基本面数据获取失败:', error.response?.data?.message || error.message);
  }

  // Test 6: Test API
  try {
    console.log('\n🔧 测试综合测试 API...');
    const response = await axios.get(`${BASE_URL}/test?ticker=${TEST_TICKER}&type=all`);
    console.log('✅ 综合测试完成');
    console.log(`   - 股票: ${response.data.ticker}`);
    console.log(`   - 测试总数: ${response.data.summary.totalTests}`);
    console.log(`   - 成功率: ${response.data.summary.successRate}`);
    console.log(`   - 平均延迟: ${response.data.summary.averageLatency}`);
    console.log(`   - 状态: ${response.data.summary.status}`);
  } catch (error) {
    console.log('❌ 综合测试失败:', error.response?.data?.message || error.message);
  }

  console.log('\n🎉 数据 API 测试完成!');
}

// 运行测试
testDataAPIs().catch(console.error);
